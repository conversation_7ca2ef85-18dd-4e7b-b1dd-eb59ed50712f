# SolidStart + SUID 示例項目

這是一個使用 **Bun + SolidStart + SUID + Azure Static Web App** 技術棧構建的前端應用示例。

## 🚀 技術棧

- **[Bun](https://bun.sh/)** - 快速的 JavaScript 運行時和包管理器
- **[SolidStart](https://start.solidjs.com/)** - SolidJS 的全棧框架
- **[SUID](https://suid.io/)** - SolidJS 版本的 Material-UI 組件庫
- **[Azure Static Web Apps](https://azure.microsoft.com/services/app-service/static/)** - 靜態網站託管服務

## 📦 功能特性

本項目展示了以下 SUID 組件的使用：

### 🎯 核心組件
- **按鈕組件** - 各種樣式和狀態的按鈕
- **卡片組件** - 信息展示卡片，包括頭像、統計數據等
- **表單組件** - 完整的表單控件，包括輸入框、選擇器、複選框等
- **導航組件** - AppBar、Drawer、Breadcrumbs、按鈕組導航

### 🎨 佈局組件
- **Grid 系統** - 響應式網格佈局
- **Container** - 容器組件
- **Stack** - 堆疊佈局

### 🔧 高級組件
- **對話框** - 模態對話框
- **通知系統** - Alert 組件
- **數據表格** - 完整的表格展示
- **徽章和標籤** - Badge 和 Chip 組件
- **加載狀態** - Skeleton 和 CircularProgress

## 🛠️ 開發環境設置

### 前置要求

- [Bun](https://bun.sh/) >= 1.0.0
- Node.js >= 22

### 安裝依賴

```bash
# 使用 Bun 安裝依賴
bun install
```

### 開發服務器

```bash
# 啟動開發服務器
bun run dev

# 服務器將在 http://localhost:3000 啟動
```

### 構建項目

```bash
# 構建生產版本
bun run build

# 預覽生產構建
bun run start
```

## 🎨 SUID 組件使用指南

### 基本設置

```tsx
import { ThemeProvider, createTheme } from "@suid/material/styles";
import { CssBaseline } from "@suid/material";

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {/* 你的應用內容 */}
    </ThemeProvider>
  );
}
```

### 組件導入

```tsx
import {
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Container
} from "@suid/material";
```

### 響應式佈局

```tsx
<Grid container spacing={2}>
  <Grid item xs={12} sm={6} md={4}>
    <Card>
      <CardContent>
        <Typography variant="h5">卡片標題</Typography>
      </CardContent>
    </Card>
  </Grid>
</Grid>
```

## ☁️ Azure Static Web Apps 部署

### 自動部署設置

1. **Fork 此倉庫**到你的 GitHub 帳戶

2. **創建 Azure Static Web App**：
   - 登錄 [Azure Portal](https://portal.azure.com)
   - 創建新的 Static Web App 資源
   - 連接到你的 GitHub 倉庫
   - 選擇 `main` 分支

3. **配置構建設置**：
   - App location: `/`
   - Api location: `` (留空)
   - Output location: `.output/public`

4. **設置 GitHub Secrets**：
   - `AZURE_STATIC_WEB_APPS_API_TOKEN` - 從 Azure Portal 獲取

### 手動部署

```bash
# 構建項目
bun run build

# 部署 .output/public 目錄到 Azure Static Web Apps
```

## 📁 項目結構

```
├── src/
│   ├── components/          # SUID 組件示例
│   │   ├── ButtonDemo.tsx   # 按鈕組件示例
│   │   ├── CardDemo.tsx     # 卡片組件示例
│   │   ├── FormDemo.tsx     # 表單組件示例
│   │   ├── GridDemo.tsx     # 網格佈局示例
│   │   ├── NavigationDemo.tsx # 導航組件示例
│   │   └── AdvancedDemo.tsx # 高級組件示例
│   ├── app.tsx              # 主應用組件
│   ├── app.css              # 全局樣式
│   └── entry-client.tsx     # 客戶端入口
├── .github/workflows/       # GitHub Actions 配置
├── staticwebapp.config.json # Azure Static Web Apps 配置
├── app.config.ts           # SolidStart 配置
└── package.json            # 項目依賴
```

## 🔧 配置說明

### SolidStart 配置 (app.config.ts)

```typescript
import { defineConfig } from "@solidjs/start/config";
import suidPlugin from "@suid/vite-plugin";

export default defineConfig({
  vite: {
    plugins: [suidPlugin()],
  },
});
```

### SUID Vite 插件

項目使用 `@suid/vite-plugin` 來優化 SUID 組件的構建和性能。

## 🎯 最佳實踐

1. **主題一致性** - 使用 ThemeProvider 確保整個應用的主題一致
2. **響應式設計** - 利用 Grid 系統和 breakpoints 實現響應式佈局
3. **性能優化** - 使用 SUID Vite 插件進行構建優化
4. **類型安全** - 充分利用 TypeScript 的類型檢查

## 🐛 故障排除

### 常見問題

1. **構建失敗** - 確保使用的 SUID 組件在當前版本中可用
2. **樣式問題** - 檢查 ThemeProvider 是否正確設置
3. **部署問題** - 確認 Azure Static Web Apps 配置正確

### 支持的 SUID 組件

本項目使用的組件都經過測試，確保與當前 SUID 版本兼容。

## 📚 相關資源

- [SolidJS 官方文檔](https://www.solidjs.com/)
- [SolidStart 文檔](https://start.solidjs.com/)
- [SUID 文檔](https://suid.io/)
- [Azure Static Web Apps 文檔](https://docs.microsoft.com/azure/static-web-apps/)
- [Bun 文檔](https://bun.sh/docs)

## 📄 許可證

MIT License - 詳見 [LICENSE](LICENSE) 文件

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

---

**使用 Bun + SolidStart + SUID 構建 © 2024**
