import { createSignal } from "solid-js";
import { ThemeProvider, createTheme } from "@suid/material/styles";
import { CssBaseline, Container, Typography, Stack, Divider } from "@suid/material";
import ButtonDemo from "./components/ButtonDemo";
import CardDemo from "./components/CardDemo";
import FormDemo from "./components/FormDemo";
import NavigationDemo from "./components/NavigationDemo";
import "./app.css";

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: [
      'Noto Sans TC',
      'Roboto',
      'Arial',
      'sans-serif',
    ].join(','),
  },
});

export default function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="lg">
        <Stack spacing={4} sx={{ py: 4 }}>
          <Typography variant="h2" component="h1" textAlign="center" gutterBottom>
            SolidStart + SUID 示例
          </Typography>
          
          <Typography variant="h6" textAlign="center" color="text.secondary">
            使用 SolidJS 和 SUID (Material-UI) 建立的組件示例
          </Typography>

          <Divider />

          {/* 導航組件示例 */}
          <NavigationDemo />
          
          <Divider />

          {/* 按鈕組件示例 */}
          <ButtonDemo />
          
          <Divider />

          {/* 卡片組件示例 */}
          <CardDemo />
          
          <Divider />

          {/* 表單組件示例 */}
          <FormDemo />
          
          <Divider />

          {/* 頁腳 */}
          <Typography variant="body2" textAlign="center" color="text.secondary" sx={{ mt: 4 }}>
            使用 Bun + SolidStart + SUID 構建 © 2024
          </Typography>
        </Stack>
      </Container>
    </ThemeProvider>
  );
}
