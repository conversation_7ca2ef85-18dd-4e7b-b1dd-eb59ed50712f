import { 
  Grid, 
  Paper, 
  Typography, 
  Box, 
  Card, 
  CardContent,
  Avatar,
  Chip,
  Stack,
  Button,
  LinearProgress
} from "@suid/material";
import { 
  TrendingUp, 
  People, 
  ShoppingCart, 
  AttachMoney,
  Visibility,
  ThumbUp,
  Comment,
  Share
} from "@suid/icons-material";
import { createSignal } from "solid-js";

export default function GridDemo() {
  const [progress] = createSignal(75);

  return (
    <Stack spacing={3} sx={{ p: 3 }}>
      <Typography variant="h4" component="h2">
        Grid 佈局組件示例
      </Typography>

      {/* 基本 Grid 佈局 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          基本響應式網格
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'primary.light', color: 'white' }}>
              <Typography variant="h6">xs=12 sm=6 md=3</Typography>
              <Typography variant="body2">響應式列</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'secondary.light', color: 'white' }}>
              <Typography variant="h6">xs=12 sm=6 md=3</Typography>
              <Typography variant="body2">響應式列</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'success.light', color: 'white' }}>
              <Typography variant="h6">xs=12 sm=6 md=3</Typography>
              <Typography variant="body2">響應式列</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'warning.light', color: 'white' }}>
              <Typography variant="h6">xs=12 sm=6 md=3</Typography>
              <Typography variant="body2">響應式列</Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* 儀表板佈局示例 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          儀表板佈局示例
        </Typography>
        <Grid container spacing={3}>
          {/* 統計卡片 */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="primary">
                      1,234
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      總用戶數
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <People />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="success.main">
                      $12,345
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      總收入
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <AttachMoney />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="warning.main">
                      567
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      訂單數量
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <ShoppingCart />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="info.main">
                      89%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      增長率
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <TrendingUp />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* 圖表區域 */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  銷售趨勢
                </Typography>
                <Box sx={{ height: 200, bgcolor: 'grey.100', borderRadius: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    圖表區域 (可集成 Chart.js 或其他圖表庫)
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* 進度指標 */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  項目進度
                </Typography>
                <Stack spacing={2}>
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">網站開發</Typography>
                      <Typography variant="body2">{progress()}%</Typography>
                    </Box>
                    <LinearProgress variant="determinate" value={progress()} />
                  </Box>
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">移動應用</Typography>
                      <Typography variant="body2">60%</Typography>
                    </Box>
                    <LinearProgress variant="determinate" value={60} color="secondary" />
                  </Box>
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">API 開發</Typography>
                      <Typography variant="body2">90%</Typography>
                    </Box>
                    <LinearProgress variant="determinate" value={90} color="success" />
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* 社交媒體卡片 */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  最新動態
                </Typography>
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar>張</Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body1">
                        張三發布了新的項目更新
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        2 小時前
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button size="small" startIcon={<ThumbUp />}>
                      讚
                    </Button>
                    <Button size="small" startIcon={<Comment />}>
                      評論
                    </Button>
                    <Button size="small" startIcon={<Share />}>
                      分享
                    </Button>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* 標籤雲 */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  熱門標籤
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  <Chip label="React" color="primary" />
                  <Chip label="SolidJS" color="secondary" />
                  <Chip label="TypeScript" color="success" />
                  <Chip label="Material-UI" color="info" />
                  <Chip label="前端開發" color="warning" />
                  <Chip label="響應式設計" />
                  <Chip label="用戶體驗" />
                  <Chip label="性能優化" />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* 不等高佈局 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          不等高佈局示例
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: 150, bgcolor: 'primary.light', color: 'white' }}>
              <Typography variant="h6">短內容</Typography>
              <Typography variant="body2">這是一個較短的內容區域。</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: 250, bgcolor: 'secondary.light', color: 'white' }}>
              <Typography variant="h6">中等內容</Typography>
              <Typography variant="body2">
                這是一個中等長度的內容區域，包含更多的文字和信息。
                可以用來展示更詳細的描述或功能說明。
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: 200, bgcolor: 'success.light', color: 'white' }}>
              <Typography variant="h6">適中內容</Typography>
              <Typography variant="body2">
                這是一個適中長度的內容區域，展示了不同高度的佈局效果。
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  );
}
