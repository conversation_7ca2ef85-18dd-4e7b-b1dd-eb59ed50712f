import {
  Stack,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Alert,
  Backdrop,
  CircularProgress,
  Skeleton,
  Chip,
  Badge,

  IconButton,
  Fab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Switch,
  FormControlLabel,


} from "@suid/material";
import { 
  Add,
  Edit,
  Delete,
  Share,
  Save,
  FileCopy,
  Print,
  Mail,
  Notifications,
  Star,
  Favorite
} from "@suid/icons-material";
import { createSignal, For } from "solid-js";

export default function AdvancedDemo() {
  const [dialogOpen, setDialogOpen] = createSignal(false);
  const [alertOpen, setAlertOpen] = createSignal(false);
  const [backdropOpen, setBackdropOpen] = createSignal(false);
  const [loading, setLoading] = createSignal(false);

  const [switchChecked, setSwitchChecked] = createSignal(true);

  // 示例表格數據
  const tableData = [
    { id: 1, name: '張三', email: '<EMAIL>', role: '管理員', status: '活躍' },
    { id: 2, name: '李四', email: '<EMAIL>', role: '用戶', status: '活躍' },
    { id: 3, name: '王五', email: '<EMAIL>', role: '編輯', status: '離線' },
    { id: 4, name: '趙六', email: '<EMAIL>', role: '用戶', status: '活躍' },
  ];

  const handleLoadingDemo = () => {
    setLoading(true);
    setBackdropOpen(true);
    setTimeout(() => {
      setLoading(false);
      setBackdropOpen(false);
      setAlertOpen(true);
    }, 2000);
  };

  return (
    <Stack spacing={4} sx={{ p: 3 }}>
      <Typography variant="h4" component="h2">
        高級組件示例
      </Typography>

      {/* 對話框和通知 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          對話框和通知
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
          <Button variant="contained" onClick={() => setDialogOpen(true)}>
            打開對話框
          </Button>
          <Button variant="outlined" onClick={() => setAlertOpen(true)}>
            顯示通知
          </Button>
          <Button variant="contained" color="secondary" onClick={handleLoadingDemo}>
            加載演示
          </Button>
        </Stack>

        {/* 對話框 */}
        <Dialog open={dialogOpen()} onClose={() => setDialogOpen(false)}>
          <DialogTitle>確認操作</DialogTitle>
          <DialogContent>
            <Typography>
              您確定要執行此操作嗎？此操作無法撤銷。
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>取消</Button>
            <Button variant="contained" onClick={() => setDialogOpen(false)}>
              確認
            </Button>
          </DialogActions>
        </Dialog>

        {/* 通知條 */}
        {alertOpen() && (
          <Alert
            severity="success"
            onClose={() => setAlertOpen(false)}
            sx={{ mb: 2 }}
          >
            操作成功完成！
          </Alert>
        )}

        {/* 背景遮罩 */}
        <Backdrop open={backdropOpen()} sx={{ zIndex: 1300 }}>
          <CircularProgress color="inherit" />
        </Backdrop>
      </Box>

      {/* 骨架屏和加載狀態 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          骨架屏和加載狀態
        </Typography>
        <Stack spacing={2}>
          {loading() ? (
            <>
              <Skeleton variant="text" width="60%" height={40} />
              <Skeleton variant="rectangular" width="100%" height={118} />
              <Skeleton variant="circular" width={40} height={40} />
            </>
          ) : (
            <>
              <Typography variant="h6">內容已加載</Typography>
              <Paper sx={{ p: 2 }}>
                <Typography>這是實際的內容區域。</Typography>
              </Paper>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton>
                  <Star />
                </IconButton>
                <Typography>用戶頭像和操作</Typography>
              </Box>
            </>
          )}
        </Stack>
      </Box>

      {/* 徽章和工具提示 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          徽章和工具提示
        </Typography>
        <Stack direction="row" spacing={3} alignItems="center">
          <Badge badgeContent={4} color="primary">
            <Notifications />
          </Badge>

          <Badge badgeContent={12} color="secondary">
            <Favorite />
          </Badge>

          <Badge badgeContent={99} color="error">
            <Mail />
          </Badge>

          <Button variant="outlined">示例按鈕</Button>
        </Stack>
      </Box>

      {/* 標籤和開關 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          標籤和開關
        </Typography>
        <Stack spacing={3}>
          <Box>
            <Typography variant="h6" sx={{ mb: 1 }}>
              技能標籤
            </Typography>
            <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
              <Chip label="JavaScript" color="primary" onDelete={() => {}} />
              <Chip label="TypeScript" color="secondary" />
              <Chip label="React" color="success" onDelete={() => {}} />
              <Chip label="SolidJS" color="info" />
              <Chip label="Vue.js" color="warning" onDelete={() => {}} />
            </Stack>
          </Box>

          <FormControlLabel
            control={
              <Switch
                checked={switchChecked()}
                onChange={(e) => setSwitchChecked(e.target.checked)}
              />
            }
            label="啟用通知"
          />
        </Stack>
      </Box>

      {/* 數據表格 */}
      <Box>
        <Typography variant="h5" sx={{ mb: 2 }}>
          數據表格
        </Typography>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>姓名</TableCell>
                <TableCell>郵箱</TableCell>
                <TableCell>角色</TableCell>
                <TableCell>狀態</TableCell>
                <TableCell>操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <For each={tableData}>
                {(row) => (
                  <TableRow>
                    <TableCell>{row.id}</TableCell>
                    <TableCell>{row.name}</TableCell>
                    <TableCell>{row.email}</TableCell>
                    <TableCell>
                      <Chip 
                        label={row.role} 
                        size="small"
                        color={row.role === '管理員' ? 'primary' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={row.status} 
                        size="small"
                        color={row.status === '活躍' ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <IconButton size="small">
                          <Edit />
                        </IconButton>
                        <IconButton size="small" color="error">
                          <Delete />
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                )}
              </For>
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* 浮動操作按鈕 */}
      <Box sx={{ position: 'fixed', bottom: 16, right: 16 }}>
        <Fab color="primary" aria-label="add">
          <Add />
        </Fab>
      </Box>
    </Stack>
  );
}
