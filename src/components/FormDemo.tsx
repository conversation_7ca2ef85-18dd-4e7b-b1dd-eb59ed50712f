import { 
  TextField, 
  Button, 
  FormControl, 
  FormLabel, 
  RadioGroup, 
  FormControlLabel, 
  Radio,
  Checkbox,
  Select,
  MenuItem,
  InputLabel,
  Stack,
  Typography,
  Paper,
  Box,
  Alert
} from "@suid/material";
import { createSignal } from "solid-js";

export default function FormDemo() {
  const [formData, setFormData] = createSignal({
    name: "",
    email: "",
    age: "",
    gender: "",
    skills: [] as string[],
    country: "",
    newsletter: false
  });

  const [submitted, setSubmitted] = createSignal(false);

  const handleSubmit = (e: Event) => {
    e.preventDefault();
    setSubmitted(true);
    setTimeout(() => setSubmitted(false), 3000);
  };

  const handleSkillChange = (skill: string, checked: boolean) => {
    const currentSkills = formData().skills;
    if (checked) {
      setFormData(prev => ({
        ...prev,
        skills: [...currentSkills, skill]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        skills: currentSkills.filter(s => s !== skill)
      }));
    }
  };

  return (
    <Stack spacing={3} sx={{ p: 3 }}>
      <Typography variant="h4" component="h2">
        表單組件示例
      </Typography>

      <Paper elevation={3} sx={{ p: 3, maxWidth: 600 }}>
        <Typography variant="h5" sx={{ mb: 3 }}>
          用戶註冊表單
        </Typography>

        {submitted() && (
          <Alert severity="success" sx={{ mb: 3 }}>
            表單提交成功！數據已保存。
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Stack spacing={3}>
            {/* 基本輸入字段 */}
            <TextField
              label="姓名"
              variant="outlined"
              fullWidth
              required
              value={formData().name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              helperText="請輸入您的真實姓名"
            />

            <TextField
              label="電子郵件"
              type="email"
              variant="outlined"
              fullWidth
              required
              value={formData().email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              helperText="我們將使用此郵件與您聯繫"
            />

            <TextField
              label="年齡"
              type="number"
              variant="outlined"
              fullWidth
              value={formData().age}
              onChange={(e) => setFormData(prev => ({ ...prev, age: e.target.value }))}
              inputProps={{ min: 1, max: 120 }}
            />

            {/* 單選按鈕組 */}
            <FormControl component="fieldset">
              <FormLabel component="legend">性別</FormLabel>
              <RadioGroup
                value={formData().gender}
                onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value }))}
              >
                <FormControlLabel value="male" control={<Radio />} label="男性" />
                <FormControlLabel value="female" control={<Radio />} label="女性" />
                <FormControlLabel value="other" control={<Radio />} label="其他" />
              </RadioGroup>
            </FormControl>

            {/* 複選框組 */}
            <FormControl component="fieldset">
              <FormLabel component="legend">技能 (可多選)</FormLabel>
              <Box sx={{ display: 'flex', flexDirection: 'column', ml: 1 }}>
                {['JavaScript', 'TypeScript', 'React', 'SolidJS', 'Vue', 'Angular'].map(skill => (
                  <FormControlLabel
                    key={skill}
                    control={
                      <Checkbox
                        checked={formData().skills.includes(skill)}
                        onChange={(e) => handleSkillChange(skill, e.target.checked)}
                      />
                    }
                    label={skill}
                  />
                ))}
              </Box>
            </FormControl>

            {/* 下拉選單 */}
            <FormControl fullWidth>
              <InputLabel>國家/地區</InputLabel>
              <Select
                value={formData().country}
                label="國家/地區"
                onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
              >
                <MenuItem value="tw">台灣</MenuItem>
                <MenuItem value="cn">中國</MenuItem>
                <MenuItem value="jp">日本</MenuItem>
                <MenuItem value="kr">韓國</MenuItem>
                <MenuItem value="us">美國</MenuItem>
                <MenuItem value="uk">英國</MenuItem>
                <MenuItem value="other">其他</MenuItem>
              </Select>
            </FormControl>

            {/* 單個複選框 */}
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData().newsletter}
                  onChange={(e) => setFormData(prev => ({ ...prev, newsletter: e.target.checked }))}
                />
              }
              label="我同意接收新聞通訊和產品更新"
            />

            {/* 提交按鈕 */}
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button 
                type="button" 
                variant="outlined"
                onClick={() => setFormData({
                  name: "",
                  email: "",
                  age: "",
                  gender: "",
                  skills: [],
                  country: "",
                  newsletter: false
                })}
              >
                重置
              </Button>
              <Button 
                type="submit" 
                variant="contained"
                disabled={!formData().name || !formData().email}
              >
                提交註冊
              </Button>
            </Box>
          </Stack>
        </form>
      </Paper>

      {/* 顯示當前表單數據 */}
      <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          當前表單數據:
        </Typography>
        <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(formData(), null, 2)}
        </Typography>
      </Paper>
    </Stack>
  );
}