import { 
  A<PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>ton, 
  IconButton,
  Menu,
  MenuI<PERSON>,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Stack,
  Tabs,
  Tab,
  Breadcrumbs,
  Link
} from "@suid/material";
import { 
  Menu as MenuIcon, 
  Home, 
  Info, 
  Settings, 
  AccountCircle,
  Dashboard,
  People,
  Analytics,
  NavigateNext
} from "@suid/icons-material";
import { createSignal } from "solid-js";

export default function NavigationDemo() {
  const [anchorEl, setAnchorEl] = createSignal<null | HTMLElement>(null);
  const [drawerOpen, setDrawerOpen] = createSignal(false);
  const [tabValue, setTabValue] = createSignal(0);

  const handleProfileMenuOpen = (event: MouseEvent) => {
    setAnchorEl(event.currentTarget as HTMLElement);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const drawerList = () => (
    <Box sx={{ width: 250 }}>
      <List>
        <ListItem disablePadding>
          <ListItemButton>
            <ListItemIcon>
              <Home />
            </ListItemIcon>
            <ListItemText primary="首頁" />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton>
            <ListItemIcon>
              <Dashboard />
            </ListItemIcon>
            <ListItemText primary="儀表板" />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton>
            <ListItemIcon>
              <People />
            </ListItemIcon>
            <ListItemText primary="用戶管理" />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton>
            <ListItemIcon>
              <Analytics />
            </ListItemIcon>
            <ListItemText primary="數據分析" />
          </ListItemButton>
        </ListItem>
      </List>
      <Divider />
      <List>
        <ListItem disablePadding>
          <ListItemButton>
            <ListItemIcon>
              <Settings />
            </ListItemIcon>
            <ListItemText primary="設置" />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton>
            <ListItemIcon>
              <Info />
            </ListItemIcon>
            <ListItemText primary="關於" />
          </ListItemButton>
        </ListItem>
      </List>
    </Box>
  );

  return (
    <Stack spacing={3} sx={{ p: 3 }}>
      <Typography variant="h4" component="h2">
        導航組件示例
      </Typography>

      {/* 頂部導航欄 */}
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static">
          <Toolbar>
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={() => setDrawerOpen(true)}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              我的應用
            </Typography>
            <Button color="inherit">首頁</Button>
            <Button color="inherit">產品</Button>
            <Button color="inherit">服務</Button>
            <Button color="inherit">聯繫</Button>
            <IconButton
              color="inherit"
              onClick={handleProfileMenuOpen}
            >
              <AccountCircle />
            </IconButton>
          </Toolbar>
        </AppBar>

        {/* 用戶選單 */}
        <Menu
          anchorEl={anchorEl()}
          open={Boolean(anchorEl())}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleMenuClose}>個人檔案</MenuItem>
          <MenuItem onClick={handleMenuClose}>我的帳戶</MenuItem>
          <MenuItem onClick={handleMenuClose}>登出</MenuItem>
        </Menu>

        {/* 側邊抽屜 */}
        <Drawer
          anchor="left"
          open={drawerOpen()}
          onClose={() => setDrawerOpen(false)}
        >
          {drawerList()}
        </Drawer>
      </Box>

      {/* 麵包屑導航 */}
      <Breadcrumbs separator={<NavigateNext fontSize="small" />}>
        <Link underline="hover" color="inherit" href="#" onclick={(e) => e.preventDefault()}>
          首頁
        </Link>
        <Link underline="hover" color="inherit" href="#" onclick={(e) => e.preventDefault()}>
          產品
        </Link>
        <Link underline="hover" color="inherit" href="#" onclick={(e) => e.preventDefault()}>
          電子產品
        </Link>
        <Typography color="text.primary">筆記本電腦</Typography>
      </Breadcrumbs>

      {/* 標籤頁導航 */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs 
          value={tabValue()} 
          onChange={(_, newValue) => setTabValue(newValue)}
        >
          <Tab label="概述" />
          <Tab label="規格" />
          <Tab label="評價" />
          <Tab label="常見問題" />
        </Tabs>
      </Box>

      {/* 標籤頁內容 */}
      <Box sx={{ p: 2, minHeight: 200, bgcolor: 'grey.50', borderRadius: 1 }}>
        {tabValue() === 0 && (
          <Typography variant="h6">
            概述內容
            <Typography variant="body2" sx={{ mt: 1 }}>
              這裡顯示產品的基本概述信息，包括主要特性和優勢。
            </Typography>
          </Typography>
        )}
        {tabValue() === 1 && (
          <Typography variant="h6">
            技術規格
            <Typography variant="body2" sx={{ mt: 1 }}>
              詳細的技術規格和參數信息在這裡顯示。
            </Typography>
          </Typography>
        )}
        {tabValue() === 2 && (
          <Typography variant="h6">
            用戶評價
            <Typography variant="body2" sx={{ mt: 1 }}>
              用戶對產品的評價和反饋信息。
            </Typography>
          </Typography>
        )}
        {tabValue() === 3 && (
          <Typography variant="h6">
            常見問題
            <Typography variant="body2" sx={{ mt: 1 }}>
              關於產品的常見問題和解答。
            </Typography>
          </Typography>
        )}
      </Box>

      {/* 簡單的水平導航 */}
      <Stack direction="row" spacing={2} sx={{ p: 2, bgcolor: 'primary.main', borderRadius: 1 }}>
        <Button variant="contained" color="secondary">
          儀表板
        </Button>
        <Button variant="text" sx={{ color: 'white' }}>
          項目
        </Button>
        <Button variant="text" sx={{ color: 'white' }}>
          團隊
        </Button>
        <Button variant="text" sx={{ color: 'white' }}>
          設置
        </Button>
      </Stack>
    </Stack>
  );
}