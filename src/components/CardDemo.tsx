import { Card, CardContent, CardActions, Typography, Button, Avatar, Stack, Box } from "@suid/material";
import { Favorite, Share, MoreVert } from "@suid/icons-material";
import { createSignal } from "solid-js";

export default function CardDemo() {
  const [favoriteCount, setFavoriteCount] = createSignal(0);

  return (
    <Stack spacing={3} sx={{ p: 3 }}>
      <Typography variant="h4" component="h2">
        卡片組件示例
      </Typography>

      {/* 基本卡片 */}
      <Card sx={{ maxWidth: 400 }}>
        <CardContent>
          <Typography variant="h5" component="div">
            基本卡片
          </Typography>
          <Typography variant="body2" color="text.secondary">
            這是一個基本的卡片組件，展示了標題和內容的基本佈局。
          </Typography>
        </CardContent>
        <CardActions>
          <Button size="small">了解更多</Button>
          <Button size="small">分享</Button>
        </CardActions>
      </Card>

      {/* 帶頭像的卡片 */}
      <Card sx={{ maxWidth: 400 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
              張
            </Avatar>
            <Box>
              <Typography variant="h6">張三</Typography>
              <Typography variant="body2" color="text.secondary">
                前端開發工程師
              </Typography>
            </Box>
          </Box>
          <Typography variant="body2">
            熱愛技術創新，專注於 React 和 SolidJS 開發。
            喜歡分享技術經驗和最佳實踐。
          </Typography>
        </CardContent>
        <CardActions>
          <Button size="small" startIcon={<Favorite />}>
            關注
          </Button>
          <Button size="small" startIcon={<Share />}>
            分享
          </Button>
        </CardActions>
      </Card>

      {/* 互動卡片 */}
      <Card sx={{ maxWidth: 400 }}>
        <CardContent>
          <Typography variant="h5" component="div">
            互動卡片
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            這個卡片展示了互動功能，包括點讚計數。
          </Typography>
          <Typography variant="h6" color="primary">
            點讚數: {favoriteCount()}
          </Typography>
        </CardContent>
        <CardActions>
          <Button 
            size="small" 
            startIcon={<Favorite />}
            onClick={() => setFavoriteCount(favoriteCount() + 1)}
          >
            點讚
          </Button>
          <Button 
            size="small" 
            onClick={() => setFavoriteCount(0)}
          >
            重置
          </Button>
        </CardActions>
      </Card>

      {/* 產品卡片 */}
      <Card sx={{ maxWidth: 400 }}>
        <CardContent>
          <Typography variant="h5" component="div">
            SolidJS 框架
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            一個宣告式、高效且靈活的 JavaScript 庫，用於構建用戶界面。
          </Typography>
          <Typography variant="h6" color="primary">
            免費使用
          </Typography>
          <Typography variant="body2" color="text.secondary">
            MIT 授權
          </Typography>
        </CardContent>
        <CardActions>
          <Button size="small" variant="contained">
            立即使用
          </Button>
          <Button size="small" variant="outlined">
            了解更多
          </Button>
        </CardActions>
      </Card>

      {/* 統計卡片 */}
      <Stack direction="row" spacing={2} sx={{ flexWrap: 'wrap' }}>
        <Card sx={{ minWidth: 200 }}>
          <CardContent>
            <Typography variant="h4" color="primary">
              1,234
            </Typography>
            <Typography variant="body2" color="text.secondary">
              總用戶數
            </Typography>
          </CardContent>
        </Card>
        
        <Card sx={{ minWidth: 200 }}>
          <CardContent>
            <Typography variant="h4" color="success.main">
              98.5%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              系統穩定性
            </Typography>
          </CardContent>
        </Card>
        
        <Card sx={{ minWidth: 200 }}>
          <CardContent>
            <Typography variant="h4" color="warning.main">
              567
            </Typography>
            <Typography variant="body2" color="text.secondary">
              活躍項目
            </Typography>
          </CardContent>
        </Card>
      </Stack>
    </Stack>
  );
}