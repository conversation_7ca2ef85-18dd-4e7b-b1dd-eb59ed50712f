import { Button, Stack, Typography } from "@suid/material";
import { Add, Delete, Edit } from "@suid/icons-material";
import { createSignal } from "solid-js";

export default function ButtonDemo() {
  const [count, setCount] = createSignal(0);

  return (
    <Stack spacing={3} sx={{ p: 3 }}>
      <Typography variant="h4" component="h2">
        按鈕組件示例
      </Typography>
      
      {/* 基本按鈕 */}
      <Stack spacing={2} direction="row">
        <Button variant="text">文本按鈕</Button>
        <Button variant="contained">包含按鈕</Button>
        <Button variant="outlined">輪廓按鈕</Button>
      </Stack>

      {/* 帶圖標的按鈕 */}
      <Stack spacing={2} direction="row">
        <Button variant="contained" startIcon={<Add />}>
          新增
        </Button>
        <Button variant="outlined" startIcon={<Edit />}>
          編輯
        </Button>
        <Button variant="contained" color="error" startIcon={<Delete />}>
          刪除
        </Button>
      </Stack>

      {/* 不同顏色的按鈕 */}
      <Stack spacing={2} direction="row">
        <Button variant="contained" color="primary">
          主要
        </Button>
        <Button variant="contained" color="secondary">
          次要
        </Button>
        <Button variant="contained" color="success">
          成功
        </Button>
        <Button variant="contained" color="warning">
          警告
        </Button>
        <Button variant="contained" color="error">
          錯誤
        </Button>
      </Stack>

      {/* 互動按鈕 */}
      <Stack spacing={2} direction="row" alignItems="center">
        <Button 
          variant="contained" 
          onClick={() => setCount(count() + 1)}
        >
          點擊計數
        </Button>
        <Typography variant="h6">
          點擊次數: {count()}
        </Typography>
        <Button 
          variant="outlined" 
          onClick={() => setCount(0)}
        >
          重置
        </Button>
      </Stack>

      {/* 不同大小的按鈕 */}
      <Stack spacing={2} direction="row" alignItems="center">
        <Button variant="contained" size="small">
          小按鈕
        </Button>
        <Button variant="contained" size="medium">
          中按鈕
        </Button>
        <Button variant="contained" size="large">
          大按鈕
        </Button>
      </Stack>

      {/* 禁用狀態 */}
      <Stack spacing={2} direction="row">
        <Button variant="contained" disabled>
          禁用按鈕
        </Button>
        <Button variant="outlined" disabled>
          禁用輪廓
        </Button>
      </Stack>
    </Stack>
  );
}