{"routes": [{"route": "/*", "serve": "/index.html", "statusCode": 200}], "navigationFallback": {"rewrite": "/index.html", "exclude": ["/images/*.{png,jpg,gif}", "/css/*"]}, "responseOverrides": {"400": {"rewrite": "/custom-400.html"}, "401": {"redirect": "/login", "statusCode": 302}, "403": {"rewrite": "/custom-403.html"}, "404": {"rewrite": "/custom-404.html"}}, "globalHeaders": {"content-security-policy": "default-src https: 'unsafe-eval' 'unsafe-inline'; object-src 'none'"}, "mimeTypes": {".json": "text/json"}, "platform": {"apiRuntime": "node:18"}, "trailingSlash": "auto", "forwardingGateway": {"allowedForwardedHosts": ["example.org", "www.example.org"], "requiredHeaders": {"X-Azure-FDID": "692a448c-2b5d-4e8d-9d0a-3c8c4b9a5c8d"}}}