name: Azure Static Web Apps CI/CD

on:
  push:
    branches:
      - main
      - master
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main
      - master

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          lfs: false

      # Setup Bun
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      # Install dependencies
      - name: Install dependencies
        run: bun install

      # Build the application
      - name: Build application
        run: bun run build

      # Deploy to Azure Static Web Apps
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          ###### Repository/Build Configurations ######
          app_location: "/" # App source code path
          api_location: "" # Api source code path - optional
          output_location: ".output/public" # Built app content directory - SolidStart output
          ###### End of Repository/Build Configurations ######
          skip_app_build: true # We build the app in the previous step

  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
      - name: Close Pull Request
        id: closepullrequest
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          action: "close"
